<?php
// Simple debug script to check shipping methods
try {
    $pdo = new PDO('mysql:host=localhost;dbname=nandini', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "=== RAW DATABASE DATA ===\n";
    $stmt = $pdo->query('SELECT id, name, cost, minimum_order_amount, is_active, sort_order FROM shipping_methods ORDER BY sort_order');
    $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($methods as $method) {
        $status = $method['is_active'] ? 'ACTIVE' : 'INACTIVE';
        echo "ID: {$method['id']}, Name: {$method['name']}, Cost: \${$method['cost']}, Min: \${$method['minimum_order_amount']}, Status: {$status}, Sort: {$method['sort_order']}\n";
    }

    echo "\n=== ACTIVE METHODS FOR CHECKOUT ===\n";
    $stmt = $pdo->query('SELECT * FROM shipping_methods WHERE is_active = 1 ORDER BY sort_order');
    $activeMethods = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($activeMethods as $method) {
        echo "ID: {$method['id']}\n";
        echo "Name: {$method['name']}\n";
        echo "Cost: \${$method['cost']}\n";
        echo "Min Order: \${$method['minimum_order_amount']}\n";
        echo "Sort Order: {$method['sort_order']}\n";
        echo "---\n";
    }

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
